import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { jwtVerify } from 'jose';
import { db } from '@/lib/db';
import { colleges, courses, collegesCourses, reviews, adminUsers, adminActivityLogs } from '@/lib/db/schema';
import { eq, and, desc, count, sql, like, or, avg } from 'drizzle-orm';

const collegesRoutes = new Hono();

// Validation schemas
const collegeSchema = z.object({
  name: z.string().min(2, 'College name must be at least 2 characters'),
  shortName: z.string().optional(),
  description: z.string().optional(),
  about: z.string().optional(),
  vision: z.string().optional(),
  mission: z.string().optional(),
  establishedYear: z.number().int().min(1800).max(new Date().getFullYear()).optional(),
  collegeType: z.enum(['Government', 'Private', 'Deemed', 'Autonomous']).optional(),
  affiliation: z.string().optional(),
  approvals: z.array(z.string()).optional(),
  accreditations: z.array(z.string()).optional(),
  location: z.object({
    address: z.string(),
    city: z.string(),
    state: z.string(),
    country: z.string().default('India'),
    pincode: z.string(),
    coordinates: z.object({
      lat: z.number(),
      lng: z.number(),
    }).optional(),
  }),
  contactInfo: z.object({
    phone: z.string().optional(),
    email: z.string().email().optional(),
    website: z.string().url().optional(),
    fax: z.string().optional(),
  }),
  logo: z.string().url().optional(),
  bannerImages: z.array(z.string().url()).optional(),
  nirfRanking: z.number().int().positive().optional(),
  totalStudents: z.number().int().positive().optional(),
  facultyCount: z.number().int().positive().optional(),
  campusArea: z.string().optional(),
  isVerified: z.boolean().default(false),
  isFeatured: z.boolean().default(false),
  isPublished: z.boolean().default(false),
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.string().optional(),
});

// Helper function to generate slug
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

// JWT Secret
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key');

// Verify JWT token
async function verifyToken(token: string) {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload;
  } catch (error) {
    return null;
  }
}

// Admin authentication middleware
async function verifyAdminAuth(c: any, next: any) {
  try {
    const authHeader = c.req.header('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'No token provided' }, 401);
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);

    if (!payload) {
      return c.json({ error: 'Invalid token' }, 401);
    }

    // Get admin user
    const [admin] = await db
      .select()
      .from(adminUsers)
      .where(and(
        eq(adminUsers.id, payload.id as string),
        eq(adminUsers.isActive, true)
      ))
      .limit(1);

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401);
    }

    c.set('admin', admin);
    await next();
  } catch (error) {
    console.error('Auth verification error:', error);
    return c.json({ error: 'Unauthorized' }, 401);
  }
}

// Permission check middleware
function requirePermission(permission: string) {
  return async (c: any, next: any) => {
    const admin = c.get('admin') as any;

    // Super admin has all permissions
    if (admin.role === 'super_admin') {
      await next();
      return;
    }

    // Check role-based permissions
    const rolePermissions: Record<string, string[]> = {
      'content_manager': ['manage_colleges', 'manage_courses', 'manage_articles', 'manage_seo'],
      'review_moderator': ['manage_reviews', 'view_reviews'],
      'lead_manager': ['manage_leads', 'manage_scholarships', 'view_leads'],
      'finance_officer': ['manage_admissions', 'view_financials', 'manage_scholarships'],
      'seo_specialist': ['manage_seo', 'view_analytics'],
    };

    const userPermissions = rolePermissions[admin.role] || [];

    if (!userPermissions.includes(permission)) {
      return c.json({ error: 'Insufficient permissions' }, 403);
    }

    await next();
  };
}

// Get all colleges with filters and pagination
collegesRoutes.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '12');
    const search = c.req.query('search');
    const city = c.req.query('city');
    const state = c.req.query('state');
    const collegeType = c.req.query('collegeType');
    const isPublished = c.req.query('isPublished');
    const isFeatured = c.req.query('isFeatured');
    const sortBy = c.req.query('sortBy') || 'name';
    const sortOrder = c.req.query('sortOrder') || 'asc';
    const offset = (page - 1) * limit;

    let query = db
      .select({
        id: colleges.id,
        name: colleges.name,
        shortName: colleges.shortName,
        slug: colleges.slug,
        description: colleges.description,
        establishedYear: colleges.establishedYear,
        collegeType: colleges.collegeType,
        affiliation: colleges.affiliation,
        location: colleges.location,
        logo: colleges.logo,
        bannerImages: colleges.bannerImages,
        nirfRanking: colleges.nirfRanking,
        overallRating: colleges.overallRating,
        totalReviews: colleges.totalReviews,
        isVerified: colleges.isVerified,
        isFeatured: colleges.isFeatured,
        isPublished: colleges.isPublished,
        createdAt: colleges.createdAt,
      })
      .from(colleges);

    // Apply filters
    const conditions = [];

    if (search) {
      conditions.push(
        or(
          like(colleges.name, `%${search}%`),
          like(colleges.shortName, `%${search}%`),
          like(colleges.description, `%${search}%`)
        )
      );
    }

    if (city) {
      conditions.push(sql`${colleges.location}->>'city' ILIKE ${`%${city}%`}`);
    }

    if (state) {
      conditions.push(sql`${colleges.location}->>'state' ILIKE ${`%${state}%`}`);
    }

    if (collegeType) {
      conditions.push(eq(colleges.collegeType, collegeType));
    }

    if (isPublished !== undefined) {
      conditions.push(eq(colleges.isPublished, isPublished === 'true'));
    }

    if (isFeatured !== undefined) {
      conditions.push(eq(colleges.isFeatured, isFeatured === 'true'));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Apply sorting
    const orderColumn = sortBy === 'rating' ? colleges.overallRating :
                       sortBy === 'reviews' ? colleges.totalReviews :
                       sortBy === 'established' ? colleges.establishedYear :
                       sortBy === 'ranking' ? colleges.nirfRanking :
                       colleges.name;

    const orderDirection = sortOrder === 'desc' ? desc(orderColumn) : orderColumn;

    const [collegesList, totalCount] = await Promise.all([
      query.orderBy(orderDirection).limit(limit).offset(offset),
      db.select({ count: count() }).from(colleges).where(conditions.length > 0 ? and(...conditions) : undefined),
    ]);

    const totalPages = Math.ceil(totalCount[0].count / limit);

    return c.json({
      success: true,
      data: collegesList,
      meta: {
        page,
        limit,
        total: totalCount[0].count,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('Get colleges error:', error);
    return c.json({ error: 'Failed to fetch colleges' }, 500);
  }
});

// Get college by ID or slug
collegesRoutes.get('/:identifier', async (c) => {
  try {
    const identifier = c.req.param('identifier');

    // Check if identifier is UUID or slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(identifier);

    const [college] = await db
      .select()
      .from(colleges)
      .where(isUUID ? eq(colleges.id, identifier) : eq(colleges.slug, identifier))
      .limit(1);

    if (!college) {
      return c.json({ error: 'College not found' }, 404);
    }

    // Get college courses
    const collegeCourses = await db
      .select({
        id: courses.id,
        name: courses.name,
        level: courses.level,
        duration: courses.duration,
        stream: courses.stream,
        fees: collegesCourses.fees,
        seats: collegesCourses.seats,
        specializations: collegesCourses.specializations,
        entranceExams: collegesCourses.entranceExams,
      })
      .from(collegesCourses)
      .innerJoin(courses, eq(collegesCourses.courseId, courses.id))
      .where(and(
        eq(collegesCourses.collegeId, college.id),
        eq(collegesCourses.isActive, true)
      ));

    // Get college reviews summary
    const reviewsSummary = await db
      .select({
        totalReviews: count(),
        averageRating: avg(reviews.overallRating),
      })
      .from(reviews)
      .where(and(
        eq(reviews.collegeId, college.id),
        eq(reviews.status, 'approved')
      ));

    return c.json({
      success: true,
      data: {
        ...college,
        courses: collegeCourses,
        reviewsSummary: reviewsSummary[0] || { totalReviews: 0, averageRating: null },
      },
    });
  } catch (error) {
    console.error('Get college error:', error);
    return c.json({ error: 'Failed to fetch college' }, 500);
  }
});

// Create new college (Admin only)
collegesRoutes.post('/', verifyAdminAuth, requirePermission('manage_colleges'), zValidator('json', collegeSchema), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const collegeData = c.req.valid('json');

    // Generate slug from name
    const slug = generateSlug(collegeData.name);

    // Check if slug already exists
    const [existingCollege] = await db
      .select()
      .from(colleges)
      .where(eq(colleges.slug, slug))
      .limit(1);

    if (existingCollege) {
      return c.json({ error: 'College with this name already exists' }, 400);
    }

    const [newCollege] = await db
      .insert(colleges)
      .values({
        ...collegeData,
        slug,
        createdBy: admin.id,
      })
      .returning();

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'create_college',
      resource: 'colleges',
      resourceId: newCollege.id,
      details: { name: collegeData.name, slug },
    });

    return c.json({
      success: true,
      data: newCollege,
      message: 'College created successfully',
    });
  } catch (error) {
    console.error('Create college error:', error);
    return c.json({ error: 'Failed to create college' }, 500);
  }
});

// Update college (Admin only)
collegesRoutes.put('/:id', verifyAdminAuth, requirePermission('manage_colleges'), zValidator('json', collegeSchema.partial()), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const collegeId = c.req.param('id');
    const updateData = c.req.valid('json');

    // If name is being updated, regenerate slug
    if (updateData.name) {
      const newSlug = generateSlug(updateData.name);

      // Check if new slug conflicts with existing college
      const [existingCollege] = await db
        .select()
        .from(colleges)
        .where(and(
          eq(colleges.slug, newSlug),
          sql`${colleges.id} != ${collegeId}`
        ))
        .limit(1);

      if (existingCollege) {
        return c.json({ error: 'College with this name already exists' }, 400);
      }

      updateData.slug = newSlug;
    }

    const [updatedCollege] = await db
      .update(colleges)
      .set({
        ...updateData,
        updatedAt: new Date(),
      })
      .where(eq(colleges.id, collegeId))
      .returning();

    if (!updatedCollege) {
      return c.json({ error: 'College not found' }, 404);
    }

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'update_college',
      resource: 'colleges',
      resourceId: collegeId,
      details: { name: updatedCollege.name, changes: Object.keys(updateData) },
    });

    return c.json({
      success: true,
      data: updatedCollege,
      message: 'College updated successfully',
    });
  } catch (error) {
    console.error('Update college error:', error);
    return c.json({ error: 'Failed to update college' }, 500);
  }
});

// Delete college (Admin only)
collegesRoutes.delete('/:id', verifyAdminAuth, requirePermission('manage_colleges'), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const collegeId = c.req.param('id');

    const [deletedCollege] = await db
      .delete(colleges)
      .where(eq(colleges.id, collegeId))
      .returning({ id: colleges.id, name: colleges.name });

    if (!deletedCollege) {
      return c.json({ error: 'College not found' }, 404);
    }

    // Log activity
    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'delete_college',
      resource: 'colleges',
      resourceId: collegeId,
      details: { name: deletedCollege.name },
    });

    return c.json({
      success: true,
      message: 'College deleted successfully',
    });
  } catch (error) {
    console.error('Delete college error:', error);
    return c.json({ error: 'Failed to delete college' }, 500);
  }
});

// Update college status (Admin only)
collegesRoutes.patch('/:id/status', verifyAdminAuth, requirePermission('manage_colleges'), async (c) => {
  try {
    const admin = c.get('admin') as any;
    const collegeId = c.req.param('id');
    const { isPublished, isVerified, isFeatured } = await c.req.json();

    // Check if college exists
    const [existingCollege] = await db
      .select()
      .from(colleges)
      .where(eq(colleges.id, collegeId))
      .limit(1);

    if (!existingCollege) {
      return c.json({ error: 'College not found' }, 404);
    }

    // Update status
    const updateData: any = { updatedAt: new Date() };
    if (isPublished !== undefined) updateData.isPublished = isPublished;
    if (isVerified !== undefined) updateData.isVerified = isVerified;
    if (isFeatured !== undefined) updateData.isFeatured = isFeatured;

    const [updatedCollege] = await db
      .update(colleges)
      .set(updateData)
      .where(eq(colleges.id, collegeId))
      .returning();

    // Log activity
    const statusChanges = Object.entries({ isPublished, isVerified, isFeatured })
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `${key}: ${value}`);

    await db.insert(adminActivityLogs).values({
      adminId: admin.id,
      action: 'update_college_status',
      resource: 'colleges',
      resourceId: collegeId,
      details: { name: updatedCollege.name, changes: statusChanges },
    });

    return c.json({
      success: true,
      data: updatedCollege,
      message: 'College status updated successfully',
    });
  } catch (error) {
    console.error('Update college status error:', error);
    return c.json({ error: 'Failed to update college status' }, 500);
  }
});

// Get college statistics
collegesRoutes.get('/stats/overview', async (c) => {
  try {
    const [
      totalColleges,
      publishedColleges,
      featuredColleges,
      verifiedColleges,
      collegesByType,
      collegesByState,
    ] = await Promise.all([
      db.select({ count: count() }).from(colleges),
      db.select({ count: count() }).from(colleges).where(eq(colleges.isPublished, true)),
      db.select({ count: count() }).from(colleges).where(eq(colleges.isFeatured, true)),
      db.select({ count: count() }).from(colleges).where(eq(colleges.isVerified, true)),
      db.select({
        type: colleges.collegeType,
        count: count(),
      }).from(colleges).groupBy(colleges.collegeType),
      // Skip location-based stats for now until migration is complete
      Promise.resolve([]),
    ]);

    return c.json({
      success: true,
      data: {
        total: totalColleges[0].count,
        published: publishedColleges[0].count,
        featured: featuredColleges[0].count,
        verified: verifiedColleges[0].count,
        byType: collegesByType,
        byState: [], // Will be populated after migration
      },
    });
  } catch (error) {
    console.error('Get college stats error:', error);
    return c.json({ error: 'Failed to fetch college statistics' }, 500);
  }
});

export { collegesRoutes };
