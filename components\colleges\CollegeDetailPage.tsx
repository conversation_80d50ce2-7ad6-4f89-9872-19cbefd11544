'use client';

import { useState } from 'react';
import { CollegeHeader } from './detail/CollegeHeader';
import { CollegeNavigation } from './detail/CollegeNavigation';
import { OverviewTab } from './detail/OverviewTab';
import { CoursesTab } from './detail/CoursesTab';
import { AdmissionTab } from './detail/AdmissionTab';
import { PlacementsTab } from './detail/PlacementsTab';
import { ReviewsTab } from './detail/ReviewsTab';
import { FacilitiesTab } from './detail/FacilitiesTab';
import { GalleryTab } from './detail/GalleryTab';

// Mock college data - In real app, this would come from API
const mockCollege = {
  id: '1',
  name: 'Indian Institute of Technology Delhi',
  shortName: 'IIT Delhi',
  tagline: 'Excellence in Engineering and Technology',
  location: 'Hauz Khas, New Delhi, Delhi 110016',
  establishedYear: 1961,
  collegeType: 'Government',
  affiliation: 'Autonomous',
  approvals: ['AICTE', 'UGC', 'MHRD'],
  accreditations: ['NAAC A++', 'NBA', 'NIRF'],
  nirfRanking: 2,
  overallRating: 4.8,
  totalReviews: 2450,
  logo: '/placeholder-logo.png',
  bannerImages: ['/placeholder-college.jpg'],
  website: 'https://www.iitd.ac.in',
  phone: '+91-11-2659-1000',
  email: '<EMAIL>',
  about: 'IIT Delhi is one of the premier engineering institutions in India...',
  vision: 'To be a world-class institution...',
  mission: 'To generate knowledge and impart education...',
  totalStudents: 8500,
  facultyCount: 650,
  campusArea: '325 acres',
  placementStats: {
    placementPercentage: 95,
    averagePackage: 1800000,
    highestPackage: 5500000,
    medianPackage: 1600000,
    topRecruiters: ['Google', 'Microsoft', 'Amazon', 'Goldman Sachs'],
  },
};

interface Props {
  collegeId: string;
}

export function CollegeDetailPage({ collegeId }: Props) {
  const [activeTab, setActiveTab] = useState('overview');

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <OverviewTab college={mockCollege} />;
      case 'courses':
        return <CoursesTab collegeId={collegeId} />;
      case 'admission':
        return <AdmissionTab collegeId={collegeId} />;
      case 'placements':
        return <PlacementsTab college={mockCollege} />;
      case 'reviews':
        return <ReviewsTab collegeId={collegeId} />;
      case 'facilities':
        return <FacilitiesTab collegeId={collegeId} />;
      case 'gallery':
        return <GalleryTab collegeId={collegeId} />;
      default:
        return <OverviewTab college={mockCollege} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* College Header */}
      <CollegeHeader college={mockCollege} />

      {/* Navigation */}
      <CollegeNavigation activeTab={activeTab} onTabChange={setActiveTab} />

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderTabContent()}
      </div>
    </div>
  );
}
