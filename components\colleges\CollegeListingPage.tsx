'use client';

import { useState } from 'react';
import { SearchAndFilters } from './SearchAndFilters';
import { CollegeCard } from './CollegeCard';
import { MapView } from './MapView';
import { Pagination } from '@/components/ui/Pagination';

// Mock data - In real app, this would come from API
const mockColleges = [
  {
    id: '1',
    name: 'Indian Institute of Technology Delhi',
    shortName: 'IIT Delhi',
    location: 'New Delhi, Delhi',
    logo: '/placeholder-logo.png',
    bannerImage: '/placeholder-college.jpg',
    rating: 4.8,
    reviewCount: 2450,
    establishedYear: 1961,
    collegeType: 'Government',
    nirfRanking: 2,
    placementPercentage: 95,
    averagePackage: 1800000,
    highestPackage: 5500000,
    totalStudents: 8500,
    accreditations: ['NAAC A++', 'NBA', 'NIRF'],
    topCourses: ['B.Tech', 'M.Tech', 'PhD'],
    isVerified: true,
    isFeatured: true,
    keyHighlight: 'Top Placements',
  },
  {
    id: '2',
    name: 'All India Institute of Medical Sciences',
    shortName: 'AIIMS Delhi',
    location: 'New Delhi, Delhi',
    logo: '/placeholder-logo.png',
    bannerImage: '/placeholder-college.jpg',
    rating: 4.9,
    reviewCount: 1890,
    establishedYear: 1956,
    collegeType: 'Government',
    nirfRanking: 1,
    placementPercentage: 100,
    averagePackage: 2200000,
    highestPackage: 8000000,
    totalStudents: 3200,
    accreditations: ['NAAC A++', 'MCI'],
    topCourses: ['MBBS', 'MD', 'MS'],
    isVerified: true,
    isFeatured: true,
    keyHighlight: 'Premier Medical Institute',
  },
  // Add more mock colleges...
];

export function CollegeListingPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'map'>('grid');
  const [selectedColleges, setSelectedColleges] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    location: '',
    stream: '',
    collegeType: '',
    fees: '',
    ranking: '',
    sortBy: 'relevance',
  });

  const itemsPerPage = 12;
  const totalPages = Math.ceil(mockColleges.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentColleges = mockColleges.slice(startIndex, startIndex + itemsPerPage);

  const handleCollegeSelect = (collegeId: string) => {
    setSelectedColleges(prev => 
      prev.includes(collegeId) 
        ? prev.filter(id => id !== collegeId)
        : [...prev, collegeId].slice(0, 3) // Max 3 colleges for comparison
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Find Your Perfect College</h1>
              <p className="mt-2 text-gray-600">
                Discover and compare {mockColleges.length.toLocaleString()} colleges across India
              </p>
            </div>
            
            {/* View Mode Toggle */}
            <div className="mt-4 lg:mt-0 flex items-center space-x-2">
              <span className="text-sm text-gray-500">View:</span>
              <div className="flex rounded-lg border border-gray-300">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`px-3 py-2 text-sm font-medium rounded-l-lg ${
                    viewMode === 'grid'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Grid
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`px-3 py-2 text-sm font-medium border-l border-gray-300 ${
                    viewMode === 'list'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  List
                </button>
                <button
                  onClick={() => setViewMode('map')}
                  className={`px-3 py-2 text-sm font-medium rounded-r-lg border-l border-gray-300 ${
                    viewMode === 'map'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Map
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="lg:grid lg:grid-cols-4 lg:gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <SearchAndFilters 
              filters={filters}
              onFiltersChange={setFilters}
            />
          </div>

          {/* Main Content */}
          <div className="mt-8 lg:mt-0 lg:col-span-3">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
              <div>
                <p className="text-sm text-gray-600">
                  Showing {startIndex + 1}-{Math.min(startIndex + itemsPerPage, mockColleges.length)} of {mockColleges.length} colleges
                </p>
              </div>
              
              {/* Compare Button */}
              {selectedColleges.length > 0 && (
                <div className="mt-4 sm:mt-0">
                  <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                    Compare {selectedColleges.length} College{selectedColleges.length > 1 ? 's' : ''}
                  </button>
                </div>
              )}
            </div>

            {/* Content based on view mode */}
            {viewMode === 'map' ? (
              <MapView colleges={currentColleges} />
            ) : (
              <>
                {/* College Grid/List */}
                <div className={
                  viewMode === 'grid' 
                    ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                    : 'space-y-6'
                }>
                  {currentColleges.map((college) => (
                    <CollegeCard
                      key={college.id}
                      college={college}
                      viewMode={viewMode}
                      isSelected={selectedColleges.includes(college.id)}
                      onSelect={() => handleCollegeSelect(college.id)}
                    />
                  ))}
                </div>

                {/* Pagination */}
                <div className="mt-12">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={setCurrentPage}
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
