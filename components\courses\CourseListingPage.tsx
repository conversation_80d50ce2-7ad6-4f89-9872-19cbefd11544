'use client';

import { useState } from 'react';
import { CourseCard } from './CourseCard';
import { CourseFilters } from './CourseFilters';
import { Pagination } from '@/components/ui/Pagination';

// Mock courses data
const mockCourses = [
  {
    id: '1',
    name: 'Bachelor of Technology (B.Tech)',
    shortName: 'B.Tech',
    level: 'Undergraduate',
    duration: '4 Years',
    stream: 'Engineering',
    description: 'Comprehensive engineering program covering various specializations',
    specializations: ['Computer Science', 'Mechanical', 'Electrical', 'Civil', 'Electronics'],
    eligibility: '10+2 with Physics, Chemistry, Mathematics (60% marks)',
    entranceExams: ['JEE Main', 'JEE Advanced', 'State CET'],
    averageFees: 400000,
    averageSalary: 800000,
    topColleges: ['IIT Delhi', 'IIT Bombay', 'NIT Trichy', 'BITS Pilani'],
    careerOptions: ['Software Engineer', 'Mechanical Engineer', 'Civil Engineer', 'Electronics Engineer'],
    isPopular: true,
    demandLevel: 'High',
    jobOpportunities: 'Excellent',
  },
  {
    id: '2',
    name: 'Bachelor of Medicine and Bachelor of Surgery (MBBS)',
    shortName: 'MBBS',
    level: 'Undergraduate',
    duration: '5.5 Years',
    stream: 'Medical',
    description: 'Professional medical degree for aspiring doctors',
    specializations: ['General Medicine', 'Surgery', 'Pediatrics', 'Gynecology'],
    eligibility: '10+2 with Physics, Chemistry, Biology (60% marks)',
    entranceExams: ['NEET UG'],
    averageFees: 1500000,
    averageSalary: 1200000,
    topColleges: ['AIIMS Delhi', 'CMC Vellore', 'JIPMER', 'KGMU'],
    careerOptions: ['Doctor', 'Surgeon', 'Specialist', 'Medical Researcher'],
    isPopular: true,
    demandLevel: 'Very High',
    jobOpportunities: 'Excellent',
  },
  {
    id: '3',
    name: 'Master of Business Administration (MBA)',
    shortName: 'MBA',
    level: 'Postgraduate',
    duration: '2 Years',
    stream: 'Management',
    description: 'Advanced business management and leadership program',
    specializations: ['Finance', 'Marketing', 'HR', 'Operations', 'Strategy'],
    eligibility: 'Bachelor\'s degree in any field (50% marks)',
    entranceExams: ['CAT', 'XAT', 'GMAT', 'MAT'],
    averageFees: 2000000,
    averageSalary: 1500000,
    topColleges: ['IIM Ahmedabad', 'IIM Bangalore', 'IIM Calcutta', 'ISB Hyderabad'],
    careerOptions: ['Manager', 'Consultant', 'Analyst', 'Entrepreneur'],
    isPopular: true,
    demandLevel: 'High',
    jobOpportunities: 'Very Good',
  },
];

export function CourseListingPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    level: '',
    stream: '',
    duration: '',
    fees: '',
    sortBy: 'popularity',
  });

  const itemsPerPage = 9;
  const totalPages = Math.ceil(mockCourses.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentCourses = mockCourses.slice(startIndex, startIndex + itemsPerPage);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Explore Courses</h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover detailed information about various courses, their eligibility criteria, 
              career prospects, and top colleges offering these programs.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="lg:grid lg:grid-cols-4 lg:gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <CourseFilters 
              filters={filters}
              onFiltersChange={setFilters}
            />
          </div>

          {/* Main Content */}
          <div className="mt-8 lg:mt-0 lg:col-span-3">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
              <div>
                <p className="text-sm text-gray-600">
                  Showing {startIndex + 1}-{Math.min(startIndex + itemsPerPage, mockCourses.length)} of {mockCourses.length} courses
                </p>
              </div>
            </div>

            {/* Course Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {currentCourses.map((course) => (
                <CourseCard
                  key={course.id}
                  course={course}
                />
              ))}
            </div>

            {/* Pagination */}
            <div className="mt-12">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Popular Streams Section */}
      <div className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">Popular Streams</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
            {[
              { name: 'Engineering', icon: '⚙️', courses: 150 },
              { name: 'Medical', icon: '🏥', courses: 45 },
              { name: 'Management', icon: '💼', courses: 80 },
              { name: 'Arts', icon: '🎨', courses: 120 },
              { name: 'Science', icon: '🔬', courses: 95 },
              { name: 'Commerce', icon: '💰', courses: 70 },
            ].map((stream) => (
              <div
                key={stream.name}
                className="bg-gray-50 hover:bg-gray-100 rounded-lg p-6 text-center cursor-pointer transition-colors duration-200 border border-gray-200"
              >
                <div className="text-3xl mb-3">{stream.icon}</div>
                <h3 className="font-semibold text-gray-900 mb-1">{stream.name}</h3>
                <p className="text-sm text-gray-600">{stream.courses} courses</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Course Comparison Tool */}
      <div className="bg-blue-50 border-t border-blue-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Compare Courses</h2>
            <p className="text-xl text-gray-600 mb-8">
              Not sure which course to choose? Use our comparison tool to make an informed decision.
            </p>
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200">
              Start Course Comparison
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
