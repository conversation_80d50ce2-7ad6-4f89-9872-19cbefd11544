import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useState, useEffect } from 'react';

// Types for auth endpoints
interface LoginRequest {
  email: string;
  password: string;
}

interface LoginResponse {
  success: boolean;
  token?: string;
  user?: {
    id: string;
    email: string;
    name: string;
    role: string;
    lastLogin?: string;
  };
  error?: string;
}

interface MeResponse {
  success: boolean;
  user?: {
    id: string;
    email: string;
    name: string;
    role: string;
    isActive: boolean;
    lastLogin?: string;
  };
  error?: string;
}

interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

interface ChangePasswordResponse {
  success: boolean;
  message?: string;
  error?: string;
}

// Login mutation
export const useAdminLogin = () => {
  const queryClient = useQueryClient();

  return useMutation<LoginResponse, Error, LoginRequest>({
    mutationFn: async (data) => {
      const response = await fetch('/api/auth/admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Login failed');
      }

      return result;
    },
    onSuccess: (data) => {
      // Store token in localStorage
      if (data.success && data.token) {
        localStorage.setItem('admin_token', data.token);
        localStorage.setItem('admin_user', JSON.stringify(data.user));
      }

      // Invalidate and refetch user data
      queryClient.invalidateQueries({ queryKey: ['admin', 'me'] });
    },
  });
};

// Get current user query
export const useAdminMe = () => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return useQuery<MeResponse, Error>({
    queryKey: ['admin', 'me'],
    queryFn: async () => {
      const token = localStorage.getItem('admin_token');

      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch('/api/auth/admin/me', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        // If token is invalid, clear it
        localStorage.removeItem('admin_token');
        localStorage.removeItem('admin_user');
        throw new Error(result.error || 'Invalid token');
      }

      return result;
    },
    enabled: mounted && !!localStorage.getItem('admin_token'),
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Change password mutation
export const useAdminChangePassword = () => {
  return useMutation<ChangePasswordResponse, Error, ChangePasswordRequest>({
    mutationFn: async (data) => {
      const token = localStorage.getItem('admin_token');

      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch('/api/auth/admin/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to change password');
      }

      return result;
    },
  });
};

// Logout mutation
export const useAdminLogout = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const token = localStorage.getItem('admin_token');

      if (token) {
        // Call logout endpoint to log the activity
        await fetch('/api/auth/admin/logout', {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
      }

      // Clear local storage
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');

      return { success: true };
    },
    onSuccess: () => {
      // Clear all queries
      queryClient.clear();
    },
  });
};

// Helper hook to check if user is authenticated
export const useIsAdminAuthenticated = () => {
  const [mounted, setMounted] = useState(false);
  const [hasToken, setHasToken] = useState(false);

  useEffect(() => {
    setMounted(true);
    setHasToken(!!localStorage.getItem('admin_token'));
  }, []);

  const { data, isLoading, error } = useAdminMe();

  return {
    isAuthenticated: mounted && !!data?.success && !!data?.user,
    user: data?.user,
    isLoading: mounted && hasToken ? isLoading : false, // Only show loading if we have a token and are mounted
    error,
  };
};

// Helper function to get auth headers
export const getAuthHeaders = () => {
  if (typeof window === 'undefined') return {};
  const token = localStorage.getItem('admin_token');
  return token ? { Authorization: `Bearer ${token}` } : {};
};
