const { fetch } = require('undici');

async function testCollegeCreation() {
  try {
    console.log('🔍 Testing college creation...');
    
    // First, login to get the token
    const loginResponse = await fetch('http://localhost:3000/api/auth/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    });

    const loginResult = await loginResponse.json();
    
    if (!loginResponse.ok) {
      console.log('❌ Login failed:', loginResult.error);
      return;
    }
    
    console.log('✅ Login successful');
    const token = loginResult.token;
    
    // Now test college creation with minimal data
    const collegeData = {
      name: 'Test College ' + Date.now(),
      location: {
        address: 'Test Address',
        city: 'Test City',
        state: 'Test State',
        country: 'India',
        pincode: '123456'
      },
      contactInfo: {
        phone: '+91-1234567890',
        email: '<EMAIL>',
        website: 'https://testcollege.edu'
      }
    };
    
    console.log('📤 Sending college data:', JSON.stringify(collegeData, null, 2));
    
    const createResponse = await fetch('http://localhost:3000/api/admin/colleges', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(collegeData)
    });

    const createResult = await createResponse.json();
    
    console.log('📥 Response status:', createResponse.status);
    console.log('📥 Response:', JSON.stringify(createResult, null, 2));
    
    if (createResponse.ok) {
      console.log('✅ College created successfully!');
    } else {
      console.log('❌ College creation failed');
    }
    
  } catch (error) {
    console.error('❌ Error testing college creation:', error);
  }
}

testCollegeCreation();
